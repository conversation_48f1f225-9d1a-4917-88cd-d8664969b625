{
  "name": "<PERSON>",
  "location": "Brooklyn, New York, 11203",
  "email": "<EMAIL>",
  "phone": "(*************",
  "summary": "Detail-oriented technical professional with 8+ years of customer service and problem-solving experience, including remote work expertise. Proven ability to troubleshoot technical issues, communicate complex solutions clearly, and maintain excellent client relationships. Strong background in Windows, macOS, and learning management systems with experience in ticketing systems and remote collaboration tools. Seeking to leverage technical aptitude and exceptional communication skills in a remote technical support specialist role.",
  "experience": [
    {
      "company": "Orb Web Apps",
      "location": "Remote",
      "title": "Frontend Developer",
      "dates": "May 2024–Present",
      "details": [
        "Collaborated remotely with cross-functional teams using Slack, Zoom, and JIRA for issue tracking and project management",
        "Troubleshot and resolved technical issues in web applications, reducing runtime errors by 30%",
        "Provided technical support and guidance to team members on development tools and best practices",
        "Documented solutions and created knowledge base articles for common technical issues and workflows"
      ]
    },
    {
      "company": "Smile English",
      "location": "Moscow, Russia (Remote coordination)",
      "title": "English Language Teacher & Technical Support",
      "dates": "December 2018–March 2022",
      "details": [
        "Provided remote technical support for online learning platforms and educational software to 100+ families",
        "Troubleshot connectivity issues, software problems, and hardware setup via phone, email, and video chat",
        "Maintained detailed client records and ticket resolution logs using CRM systems and Excel spreadsheets",
        "Delivered step-by-step technical guidance and created user documentation for common issues",
        "Achieved 95% client retention rate through exceptional customer service and efficient problem resolution"
      ]
    },
    {
      "company": "Rite Aid",
      "location": "Lake Hiawatha, NJ",
      "title": "Store Supervisor / Manager on Duty",
      "dates": "October 2017–May 2018",
      "details": [
        "Managed point-of-sale systems, inventory management software, and cash reconciliation processes",
        "Trained team members on technology systems, troubleshooting procedures, and customer service protocols",
        "Resolved customer complaints and technical issues with payment systems and store equipment",
        "Created process documentation and maintained equipment logs to improve operational efficiency"
      ]
    }
  ],
  "education": [
    {
      "institution": "App Academy",
      "location": "New York, NY",
      "degree": "Full Stack Development Certificate",
      "date": "December 2022–April 2023",
      "details": "Intensive project-based coding bootcamp covering technical troubleshooting, database management, and web technologies"
    },
    {
      "institution": "Bergen Community College",
      "location": "Paramus, NJ",
      "degree": "Coursework in World Languages and Cultures",
      "date": "2016–2017"
    },
    {
      "institution": "Ridgefield Memorial High School",
      "location": "Ridgefield, NJ",
      "degree": "High School Diploma",
      "date": "June 2016"
    }
  ],
  "certificates": [
    {
      "name": "CELTA (Certificate in English Language Teaching to Adults)",
      "date": "August 2018–October 2018",
      "issuer": "University of Cambridge"
    }
  ],
  "technical_skills": {
    "operating_systems": [
      "Windows 10/11",
      "Linux Ubuntu",
      "Android"
    ],
    "ticketing_systems": [
      "JIRA Service Management",
      "Zendesk"
    ],
    "software_tools": [
      "Microsoft Office Suite (Excel, Word, PowerPoint, Outlook)",
      "Google Workspace (Gmail, Drive, Sheets, Docs)",
      "Slack",
      "Microsoft Teams",
      "Zoom",
      "Salesforce CRM",
    ],
    "programming_languages": [
      "JavaScript",
      "Python",
      "SQL",
      "HTML/CSS",
      "Basic scripting"
    ],
    "cloud_services": [
      "Office 365 Administration",
      "Google Workspace Administration",
      "Microsoft Azure basics",
      "AWS basics"
    ]
  },
  "core_competencies": [
    "Technical Troubleshooting",
    "Customer Service Excellence",
    "Remote Collaboration",
    "Problem-solving",
    "Cross-cultural Communication",
    "Time Management",
    "Documentation",
    "Ticket Management",
    "Hardware/Software Support",
    "Network Connectivity Issues",
    "Step-by-step Instruction",
    "Conflict Resolution"
  ]
}
