{"name": "<PERSON>", "location": "Brooklyn, New York, 11203", "email": "<EMAIL>", "phone": "(*************", "summary": "Detail-oriented technical professional with 8+ years of customer service and problem-solving experience, including remote work expertise. Proven ability to troubleshoot technical issues, communicate complex solutions clearly, and maintain excellent client relationships. Strong background in Windows, macOS, and learning management systems with experience in ticketing systems and remote collaboration tools. Seeking to leverage technical aptitude and exceptional communication skills in a remote technical support specialist role.", "experience": [{"company": "Orb Web Apps", "location": "Remote", "title": "Frontend Developer", "dates": "May 2024–Present", "details": ["Collaborated remotely with cross-functional teams using Slack, Zoom, and project management tools in agile development environment", "Troubleshot and resolved technical issues in React applications, reducing runtime errors by 30%", "Provided technical guidance and mentoring to team members on development best practices", "Integrated third-party APIs and headless CMS systems, ensuring seamless data flow and real-time updates"]}, {"company": "Travel Hotline", "location": "Remote", "title": "Full Stack Developer", "dates": "December 2023–March 2024", "details": ["Led remote cross-functional team, reviewed 100+ pull requests and provided detailed technical feedback", "Implemented CI/CD pipelines using GitHub Actions, streamlining deployment processes and reducing errors", "Mentored junior developers in troubleshooting techniques and best practices for mobile development", "Utilized ticketing systems and project management tools to track issues and coordinate team efforts"]}, {"company": "Smile English", "location": "Moscow, Russia (Remote coordination)", "title": "English Language Teacher", "dates": "December 2018–March 2022", "details": ["Provided remote technical support for online learning platforms and educational software to 100+ families", "Troubleshot connectivity issues, software problems, and hardware setup for students and parents", "Maintained detailed client records and progress reports using CRM systems and learning management platforms", "Delivered step-by-step technical guidance via phone, email, and video chat to resolve user issues", "Achieved 95% client retention rate through exceptional customer service and problem resolution"]}, {"company": "Eurofins QC Laboratories", "location": "East Rutherford, NJ", "title": "Summer Field Technician", "dates": "May 2017–September 2017", "details": ["Operated and troubleshot field testing equipment and laboratory information systems", "Documented technical findings and maintained compliance with regulatory protocols using specialized software", "Coordinated with laboratory staff to resolve sample processing issues and optimize workflows", "Utilized GPS navigation systems and mobile devices for efficient route planning and data collection"]}, {"company": "Rite Aid", "location": "Lake Hiawatha, NJ", "title": "Store Supervisor / Manager on Duty", "dates": "October 2017–May 2018", "details": ["Managed point-of-sale systems, inventory management software, and cash reconciliation processes", "Trained team members on technology systems, troubleshooting procedures, and customer service protocols", "Resolved customer complaints and technical issues with payment systems and store equipment", "Streamlined checkout processes and implemented workflow improvements to enhance customer experience"]}], "education": [{"institution": "App Academy", "location": "New York, NY", "degree": "Full Stack Development Certificate", "date": "December 2022–April 2023", "details": "Intensive project-based coding bootcamp covering technical troubleshooting, database management, and web technologies"}, {"institution": "Bergen Community College", "location": "Paramus, NJ", "degree": "Coursework in World Languages and Cultures", "date": "2016–2017"}, {"institution": "Ridgefield Memorial High School", "location": "Ridgefield, NJ", "degree": "High School Diploma", "date": "June 2016"}], "certificates": [{"name": "CELTA (Certificate in English Language Teaching to Adults)", "date": "August 2018–October 2018", "issuer": "University of Cambridge"}], "technical_skills": {"operating_systems": ["Windows", "macOS", "Linux"], "software_tools": ["Zendesk", "JIRA", "Microsoft Office Suite", "Google Workspace", "<PERSON><PERSON>ck", "Zoom", "Salesforce CRM", "Learning Management Systems"], "troubleshooting_tools": ["Remote Desktop", "Network Diagnostics", "System Monitoring Tools", "Database Management"], "programming_languages": ["JavaScript", "Python", "SQL", "HTML/CSS"], "cloud_services": ["Office 365", "Google Workspace", "Remote Development Environments"]}, "core_competencies": ["Technical Troubleshooting", "Customer Service Excellence", "Remote Collaboration", "Problem-solving", "Cross-cultural Communication", "Time Management", "Documentation", "Ticket Management", "Hardware/Software Support", "Network Connectivity Issues", "Step-by-step Instruction", "Conflict Resolution"]}