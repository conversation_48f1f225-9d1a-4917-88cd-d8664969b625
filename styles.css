
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Times New Roman', serif;
    font-size: 11pt;
    line-height: 1.2;
    color: #000;
    background: #fff;
    max-width: 7.5in;
    margin: 0 auto;
    padding: 0;
}

/* Header */
.header {
    text-align: left;
    margin-bottom: 16pt;
    border-bottom: 1pt solid #000;
    padding-bottom: 8pt;
}

.name {
    font-size: 24pt;
    font-weight: bold;
    margin-bottom: 6pt;
    letter-spacing: 0.5pt;
}

.contact {
    font-size: 11pt;
    line-height: 1.1;
}

/* Section Headers */
.section {
    margin-bottom: 14pt;
}

.section-title {
    font-size: 12pt;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 8pt;
    letter-spacing: 0.5pt;
}

/* Summary */
.summary-text {
    font-size: 11pt;
    line-height: 1.3;
    text-align: justify;
}

/* Professional Experience */
.experience {
    border-left: 1.5pt solid #000;
    margin-left: 0;
    padding-left: 12pt;
}

.job {
    margin-bottom: 16pt;
}

.job:last-child {
    margin-bottom: 0;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4pt;
}

.company-name {
    font-weight: normal;
    font-size: 11pt;
}

.job-title {
    font-weight: bold;
    font-size: 11pt;
    margin-top: 1pt;
}

.job-dates {
    font-style: italic;
    font-size: 11pt;
    text-align: right;
    white-space: nowrap;
    margin-left: 20pt;
}

.job-duties {
    margin-top: 6pt;
}

.job-duties ul {
    list-style-type: disc;
    margin-left: 18pt;
    padding-left: 0;
}

.job-duties li {
    font-size: 11pt;
    line-height: 1.25;
    margin-bottom: 3pt;
    text-align: justify;
}

.job-duties li:last-child {
    margin-bottom: 0;
}

/* Education */
.education-item {
    margin-bottom: 10pt;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.education-item:last-child {
    margin-bottom: 0;
}

.education-info {
    flex: 1;
}

.school-name {
    font-weight: normal;
    font-size: 11pt;
}

.degree {
    font-style: italic;
    font-size: 11pt;
    margin-top: 1pt;
}

.education-date {
    font-size: 11pt;
    white-space: nowrap;
    margin-left: 20pt;
}

/* Certificates */
.certificate-item {
    margin-bottom: 6pt;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.certificate-item:last-child {
    margin-bottom: 0;
}

.certificate-info {
    flex: 1;
}

.certificate-name {
    font-weight: normal;
    font-size: 11pt;
}

.issuing-org {
    font-size: 11pt;
    margin-top: 1pt;
}

.certificate-date {
    font-size: 11pt;
    white-space: nowrap;
    margin-left: 20pt;
}

/* Skills */
.skills-text {
    font-size: 11pt;
    line-height: 1.3;
}

/* @media print {
    body {
        font-size: 10pt;
    }

    .name {
        font-size: 22pt;
    }

    .section-title {
        font-size: 11pt;
    }
} */