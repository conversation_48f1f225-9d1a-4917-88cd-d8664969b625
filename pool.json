{"personal_info": {"name": "<PERSON> (<PERSON>)", "email": "<EMAIL>", "phone": "+****************", "location": "Brooklyn, NY / Hoboken, New Jersey", "linkedin": "https://www.linkedin.com/in/alexthelecky1875273", "github": "https://github.com/Luckyleck", "portfolio": "https://alexlecky.com", "other_profiles": ["Fiverr (Online English Teaching)"]}, "professional_summary": {"current_title": "Frontend Developer", "years_of_experience": "10+ years total (8+ years education/client relations, 2+ years development)", "key_strengths": ["Frontend development with React, TypeScript, JavaScript", "Full-stack development with Ruby on Rails, Node.js", "Cross-cultural communication and team leadership", "English language education and curriculum development", "Client relationship management and customer service", "Team mentoring and remote collaboration"], "career_objective": "Seeking to leverage technical skills and international experience in frontend/full-stack development roles", "elevator_pitch": "Frontend developer with unique blend of technical expertise and cross-cultural communication skills, bringing 10+ years of experience in education and client relations to collaborative development environments."}, "work_experience": [{"title": "Frontend Developer", "company": "Orb Web Apps", "location": "Remote", "duration": "05/2024 - Present", "type": "Full-time", "responsibilities": ["Led frontend development using monorepo architecture to streamline code management", "Translated Figma designs into responsive interfaces using Tailwind CSS", "Built reusable components and utility classes increasing development speed by 40%", "Integrated headless CMS (Sanity) for real-time content updates under 2 seconds", "Collaborated remotely with cross-functional teams in agile sprints"], "achievements": ["Increased development speed by 40% through reusable components", "Reduced CSS file size by 25% through optimized utility classes", "Contributed to multiple successful product launches"], "technologies": ["React", "TypeScript", "JavaScript", "Tailwind CSS", "Sanity CMS", "Monorepo Architecture"]}, {"title": "Full Stack Developer", "company": "Travel Hotline", "location": "Remote", "duration": "12/2023 - 03/2024", "type": "Contract", "responsibilities": ["Led cross-functional remote team, reviewed and provided feedback on 100+ pull requests", "Designed and deployed robust CI/CD pipeline using GitHub Actions", "Delivered reusable, type-safe React Native components using TypeScript", "Implemented Redux for scalable state management", "Mentored junior developers in full-stack mobile development best practices"], "achievements": ["Reduced runtime errors by 30% through type-safe components", "Decreased unnecessary re-renders by 50% with Redux optimization", "Successfully mentored junior developers in remote settings"], "technologies": ["React Native", "TypeScript", "Redux", "GitHub Actions", "CI/CD", "ESLint"]}, {"title": "English Language Teacher", "company": "Smile English", "location": "Moscow, Russia", "duration": "12/2018 - 03/2022", "type": "Full-time", "responsibilities": ["Delivered personalized English instruction to students of all ages", "Served as English-speaking governor for international school children", "Developed custom curricula across multiple subjects (English, math, science, history)", "Prepared students for IELTS, YLE, and Cambridge school entry exams", "Maintained regular communication with parents providing detailed progress reports", "Assisted in marketing and sales of services through social media and client outreach"], "achievements": ["Achieved 95% client retention rate while managing 100+ families", "Successfully prepared students for international exams", "Demonstrated cultural sensitivity working with elite families"], "technologies": ["Learning Management Systems", "Online Teaching Platforms", "Assessment Tools", "Social Media Marketing"]}, {"title": "Head International Teacher", "company": "Discovery English", "location": "Moscow, Russia", "duration": "09/2019 - 01/2020", "type": "Full-time", "responsibilities": ["Designed and delivered English instruction for 20 kindergarten students", "Created dynamic lesson plans using visual, auditory, and kinesthetic strategies", "Facilitated communication between parents, Russian-speaking staff, and administration", "Adapted teaching materials for multilingual environment"], "achievements": ["Accelerated early language acquisition through play-based methods", "Achieved key learning milestones ahead of schedule", "Successfully managed cross-cultural communication"], "technologies": ["Cambridge Materials", "<PERSON><PERSON>s", "Learning Management Systems"]}, {"title": "Store Supervisor/Manager ON Duty", "company": "Rite Aid", "location": "Lake Hiawatha, NJ", "duration": "10/2017 - 05/2018", "type": "Full-time", "responsibilities": ["Acted as store manager during leadership absences", "Managed team of 5+ employees and handled cash reconciliation", "Trained team members on customer service excellence and conflict resolution", "Streamlined point-of-sale and checkout processes"], "achievements": ["Improved transaction workflows leading to faster service", "Reduced errors and increased customer satisfaction", "Developed more autonomous, customer-focused team"], "technologies": ["Point-of-Sale Systems", "Inventory Management", "Cash Management Systems"]}, {"title": "Summer Field Technician", "company": "Eurofins QC Laboratories", "location": "East Rutherford, NJ", "duration": "05/2017 - 09/2017", "type": "Seasonal", "responsibilities": ["Collected water samples from client sites throughout Northern New Jersey", "Conducted field chemical testing and documented findings per regulatory protocol", "Coordinated with laboratory staff for timely sample processing", "Planned and optimized daily driving routes for efficiency"], "achievements": ["Maintained consistent compliance with state and environmental regulations", "Improved sample handling procedures increasing lab processing speed", "Built professional rapport with facility managers"], "technologies": ["Field Testing Equipment", "Laboratory Information Systems", "GPS Navigation", "Regulatory Compliance Software"]}, {"title": "Fur Department Consultant", "company": "Macy's", "location": "Paramus, NJ", "duration": "2016 - 2017", "type": "Part-time", "responsibilities": ["Cultivated loyal clientele through exceptional product knowledge and personalized service", "Maintained detailed customer records in comprehensive client database", "Orchestrated elegant merchandise displays highlighting premium collections", "Conducted proactive outreach to high-value customers about new collections"], "achievements": ["Increased seasonal sales by 15% through targeted follow-up communications", "Reduced processing time by 25% through streamlined inventory procedures", "Achieved 30% conversion rate on follow-up sales calls"], "technologies": ["Customer Database Systems", "Inventory Management", "Sales Tracking Software"]}, {"title": "Teacher", "company": "Zenith Academy", "location": "Ridgefield, NJ", "duration": "11/2015 - 07/2016", "type": "Part-time", "responsibilities": ["Helped young children with school homework in after-school program", "Provided supplementary educational work to enhance student learning", "Conducted ESL lessons for children whose first language was not English", "Communicated with parents about student progress"], "achievements": ["Successfully supported students in completing school assignments", "Improved English language skills for ESL students", "Built strong relationships with parents and students"], "technologies": ["Educational Software", "Learning Management Systems"]}, {"title": "Lifeguard", "company": "Ridgefield Town Pool", "location": "Ridgefield, NJ", "duration": "06/2014 - 08/2014", "type": "Seasonal", "responsibilities": ["Ensured swimmer safety by enforcing pool rules and monitoring activity", "Administered first aid and CPR as needed", "Maintained cleanliness and safety of pool facilities", "Provided customer service to pool visitors"], "achievements": ["Maintained safe environment for all patrons", "Successfully responded to emergency situations", "Provided excellent customer service"], "technologies": ["Safety Equipment", "First Aid Equipment", "Pool Maintenance Systems"]}], "education": [{"degree": "Full Stack Development Certificate", "institution": "App Academy", "location": "New York, NY", "duration": "Dec 2022 - Apr 2023", "type": "Intensive Bootcamp", "details": "Intensive project-based coding bootcamp covering Ruby on Rails, React, PostgreSQL, and MERN stack"}, {"degree": "CELTA (Certificate in English Language Teaching to Adults)", "institution": "University of Cambridge", "location": "London, UK", "duration": "Aug 2018 - Oct 2018", "type": "Professional Certification", "details": "Cambridge certification in English language teaching with focus on communicative teaching methods"}, {"degree": "Some College in World Languages and Cultures", "institution": "Bergen Community College", "location": "Paramus, NJ", "duration": "2016 - 2017", "type": "College Coursework", "details": "Coursework in world languages and cultures"}, {"degree": "High School Diploma", "institution": "Ridgefield Memorial High School", "location": "Ridgefield, NJ", "duration": "2016", "type": "High School", "details": "General high school diploma"}], "technical_skills": {"programming_languages": ["JavaScript", "Python", "<PERSON>", "HTML5", "CSS3", "SQL", "TypeScript"], "frameworks_libraries": ["React", "Redux", "Ruby on Rails", "Node.js", "Express.js"], "databases": ["PostgreSQL", "SQL", "Database Management"], "tools_software": ["Git/GitHub", "JIRA", "Zendesk", "Microsoft Office Suite", "Google Workspace", "Zoom", "<PERSON><PERSON>ck", "Salesforce CRM", "Advanced Excel", "Data Validation Tools"], "cloud_platforms": ["Netlify", "Remote Development Environments"], "operating_systems": ["Windows", "macOS", "Linux"], "methodologies": ["Agile Development", "Test-Driven Development", "Component-Based Architecture", "Monorepo Architecture", "ETL Processes", "Data Validation Techniques"], "other_technical": ["Bundle Optimization", "Performance Optimization", "API Integration", "Database Design", "Data Migration", "Automated Workflows"]}, "soft_skills": ["Cross-cultural Communication", "Team Leadership", "Project Management", "Problem-solving", "Customer Service Excellence", "Conflict Resolution", "Emotional Intelligence", "Public Speaking", "Mentoring and Training", "Adaptability", "Cultural Sensitivity", "Time Management"], "achievements": ["Increased development speed by 40% through reusable React components", "Reduced CSS file size by 25% through optimized Tailwind utility classes", "Reduced runtime errors by 30% through TypeScript implementation", "Decreased unnecessary re-renders by 50% with Redux optimization", "95% client retention rate managing 100+ families as English teacher", "Increased seasonal sales by 15% through targeted customer communications", "Achieved 30% conversion rate on follow-up sales calls at Macy's", "Successfully prepared students for Cambridge and IELTS exams", "Maintained consistent regulatory compliance in laboratory work"], "languages": [{"language": "English", "proficiency": "Native/Fluent"}, {"language": "Russian", "proficiency": "Conversational"}], "keywords_pool": {"industry_terms": ["Full-stack development", "Frontend development", "Data processing", "Client relations", "Customer success", "Database management", "Component libraries", "Monorepo architecture", "ETL processes", "CRM systems", "Learning management systems"], "job_titles": ["Frontend Developer", "Full Stack Developer", "English Language Teacher", "Head International Teacher", "Store Supervisor", "Manager ON Duty", "Field Technician", "Sales Consultant", "Teacher", "Lifeguard", "Software Engineer", "Web Developer"], "action_verbs": ["Developed", "Implemented", "Optimized", "Managed", "Created", "Designed", "Maintained", "Processed", "Collaborated", "Led", "Trained", "Mentored", "Analyzed", "Resolved", "Coordinated", "Facilitated"], "quantifiable_metrics": ["40% development speed increase", "25% CSS file size reduction", "30% runtime error reduction", "50% re-render reduction", "95% client retention rate", "100+ families managed", "15% seasonal sales increase", "30% conversion rate on sales calls", "20 kindergarten students taught", "5+ employees managed"], "company_types": ["Tech startups", "Educational institutions", "Remote companies", "International organizations", "Data processing companies", "Customer service organizations", "Growing companies (50-200 employees)"]}, "experience_variations": {"different_role_descriptions": ["Frontend developer with React, TypeScript, and Tailwind CSS expertise", "Full-stack developer with React Native and mobile development experience", "English language teacher with international and cross-cultural experience", "Retail management professional with customer service excellence", "Technical field worker with regulatory compliance experience"], "skill_combinations": ["Technical skills + Teaching ability = Technical training and mentoring roles", "Development skills + International experience = Global tech companies", "Customer service + Management = Team leadership and client relations", "Teaching + Technology = EdTech and educational software roles"], "industry_adaptations": ["EdTech: Combining teaching experience with frontend development skills", "E-commerce: Frontend development with customer service background", "SaaS: Technical skills with client relationship management", "International companies: Cross-cultural communication and remote collaboration"]}, "metadata": {"last_updated": "2025-07-24T14:43:52.836484", "source_documents": ["Indeed Resume.pdf", "<PERSON> - CV.pdf", "<PERSON> Cover Letter.pdf", "<PERSON> CV.pdf", "<PERSON> Full Client Support Specialist.pdf", "<PERSON>mal Resume.pdf", "<PERSON> Resume NW.pdf", "<PERSON> Resume.pdf", "<PERSON> Summary.pdf", "<PERSON> Tech Resume 2024.pdf", "<PERSON>.pdf", "<PERSON> - Client Support Specialist.pdf", "<PERSON> - Frontend Developer 5mb.pdf", "<PERSON> - Frontend Developer.pdf", "<PERSON> - Resume 2025.pdf", "<PERSON> - Support Specialist Letter.pdf", "<PERSON> - Technical Support Specialist.pdf", "<PERSON> Classic Resume.pdf", "<PERSON> Client Support Specialist Letter.pdf", "<PERSON> Cover Letter.pdf", "<PERSON>.pdf"], "parsing_notes": ["Reparsed from 21 PDF resume documents with Indeed Resume as primary source", "Corrected job titles to reflect actual roles rather than embellished versions", "Removed fabricated 'Administrative Data Coordinator' role at 'Remote Solutions Inc'", "Added complete work history including early career positions", "Added Bergen Community College education (2016-2017)", "Updated achievements to reflect accurate metrics from actual roles", "Maintained honest and accurate representation of experience"]}}