fetch('resume.json')
    .then(res => res.json())
    .then(data => {
        // Header
        document.querySelector('.name').textContent = data.name;
        document.querySelector('.contact').textContent = `${data.location} • ${data.email} • ${data.phone}`;

        // Summary
        document.querySelector('.summary-text').textContent = data.summary;

        // Experience
        const experienceContainer = document.querySelector('.experience');
        experienceContainer.innerHTML = ''; // Clear existing HTML if any

        data.experience.forEach(job => {
            const jobHTML = `
        <div class="job">
          <div class="job-header">
            <div>
              <div class="company-name">${job.company}, ${job.location}</div>
              <div class="job-title">${job.title}</div>
            </div>
            <div class="job-dates">${job.dates}</div>
          </div>
          <div class="job-duties">
            <ul>
              ${job.details.map(detail => `<li>${detail}</li>`).join('')}
            </ul>
          </div>
        </div>
      `;
            experienceContainer.insertAdjacentHTML('beforeend', jobHTML);
        });

        // Education
        const educationSection = document.querySelectorAll('.education-item');
        educationSection.forEach((eduEl, i) => {
            if (data.education[i]) {
                eduEl.querySelector('.school-name').textContent = `${data.education[i].institution}, ${data.education[i].location}`;
                eduEl.querySelector('.degree').textContent = data.education[i].degree;
                eduEl.querySelector('.education-date').textContent = data.education[i].date;
            }
        });

        // Certificates
        const cert = data.certificates[0]; // Only one in your current layout
        if (cert) {
            document.querySelector('.certificate-name').textContent = cert.name;
            document.querySelector('.issuing-org').textContent = cert.issuer;
            document.querySelector('.certificate-date').textContent = cert.date;
        }

        // Skills
        document.querySelector('.skills-text').textContent = data.skills.join(', ');
    });
