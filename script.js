fetch('resume.json')
  .then(res => res.json())
  .then(data => {
    // Header
    document.querySelector('.name').textContent = data.name;
    document.querySelector('.contact').textContent = `${data.location} • ${data.email} • ${data.phone}`;

    // Summary
    document.querySelector('.summary-text').textContent = data.summary;

    // Experience
    const experienceContainer = document.querySelector('.experience');
    experienceContainer.innerHTML = ''; // Clear existing HTML if any

    data.experience.forEach(job => {
      const jobHTML = `
        <div class="job">
          <div class="job-header">
            <div>
              <div class="company-name">${job.company}, ${job.location}</div>
              <div class="job-title">${job.title}</div>
            </div>
            <div class="job-dates">${job.dates}</div>
          </div>
          <div class="job-duties">
            <ul>
              ${job.details.map(detail => `<li>${detail}</li>`).join('')}
            </ul>
          </div>
        </div>
      `;
      experienceContainer.insertAdjacentHTML('beforeend', jobHTML);
    });

    // Education
    const educationSection = document.querySelectorAll('.education-item');
    educationSection.forEach((eduEl, i) => {
      if (data.education[i]) {
        eduEl.querySelector('.school-name').textContent = `${data.education[i].institution}, ${data.education[i].location}`;
        eduEl.querySelector('.degree').textContent = data.education[i].degree;
        eduEl.querySelector('.education-date').textContent = data.education[i].date;
      }
    });

    // Certificates
    const cert = data.certificates[0]; // Only one in your current layout
    if (cert) {
      document.querySelector('.certificate-name').textContent = cert.name;
      document.querySelector('.issuing-org').textContent = cert.issuer;
      document.querySelector('.certificate-date').textContent = cert.date;
    }

    // Technical Skills
    const technicalSkillsContainer = document.querySelector('.technical-skills');
    if (data.technical_skills) {
      let skillsHTML = '';

      // Operating Systems
      if (data.technical_skills.operating_systems) {
        skillsHTML += `<div class="skill-category">
                    <strong>Operating Systems:</strong> ${data.technical_skills.operating_systems.join(', ')}
                </div>`;
      }

      // Ticketing Systems
      if (data.technical_skills.ticketing_systems) {
        skillsHTML += `<div class="skill-category">
                    <strong>Ticketing Systems:</strong> ${data.technical_skills.ticketing_systems.join(', ')}
                </div>`;
      }

      // Software Tools
      if (data.technical_skills.software_tools) {
        skillsHTML += `<div class="skill-category">
                    <strong>Software Tools:</strong> ${data.technical_skills.software_tools.join(', ')}
                </div>`;
      }

      // Programming Languages
      if (data.technical_skills.programming_languages) {
        skillsHTML += `<div class="skill-category">
                    <strong>Programming Languages:</strong> ${data.technical_skills.programming_languages.join(', ')}
                </div>`;
      }

      // Cloud Services
      if (data.technical_skills.cloud_services) {
        skillsHTML += `<div class="skill-category">
                    <strong>Cloud Services:</strong> ${data.technical_skills.cloud_services.join(', ')}
                </div>`;
      }

      technicalSkillsContainer.innerHTML = skillsHTML;
    }

    // Core Competencies
    const coreCompetenciesContainer = document.querySelector('.core-competencies');
    if (data.core_competencies) {
      coreCompetenciesContainer.innerHTML = `<div class="competencies-list">${data.core_competencies.join(' • ')}</div>`;
    }
  });
